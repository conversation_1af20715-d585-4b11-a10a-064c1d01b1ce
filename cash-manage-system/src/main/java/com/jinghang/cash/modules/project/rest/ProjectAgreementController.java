package com.jinghang.cash.modules.project.rest;

import com.jinghang.cash.annotation.AnonymousAccess;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.project.domain.ProjectAgreement;
import com.jinghang.cash.modules.project.domain.dto.ProjectAgreementQueryCriteria;
import com.jinghang.cash.modules.project.service.ProjectAgreementService;
import com.jinghang.cash.utils.PageResult;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 协议模板配置控制器
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 10:10
 */
@RestController
@RequestMapping("/projectAgreement")
public class ProjectAgreementController {

    @Autowired
    private ProjectAgreementService projectAgreementService;

    /**
     * 根据项目代码、流程贷款阶段和合同模板类型获取项目协议
     *
     * @param projectCode          项目编码
     * @param flowLoanStage        资产方合同签署阶段
     * @param capitalLoanStage     资金方合同签署阶段
     * @param contractTemplateType 合同模板类型
     * @return 项目协议
     */
    @PostMapping("/queryByStageAndType")
    @AnonymousAccess
    public ProjectAgreement getByStageAndType(
            @RequestParam("projectCode") String projectCode,
            @RequestParam(value = "flowLoanStage", required = false) String flowLoanStage,
            @RequestParam(value = "capitalLoanStage", required = false) String capitalLoanStage,
            @RequestParam("contractTemplateType") String contractTemplateType) {
        return projectAgreementService.getByStageAndType(projectCode, flowLoanStage, capitalLoanStage, contractTemplateType);
    }

    /**
     * 根据项目代码和是否退回流程获取项目协议列表
     *
     * @param projectCodes      项目编码列表
     * @param isReturnToFlow    是否回传流量方
     * @param isReturnToCapital 是否回传资金方
     * @return 项目协议列表
     */
    @PostMapping("/queryByReturnStatus")
    @AnonymousAccess
    public List<ProjectAgreement> getByReturnStatus(
            @RequestParam("projectCodes") List<String> projectCodes,
            @RequestParam(value = "isReturnToFlow", required = false) ActiveInactive isReturnToFlow,
            @RequestParam(value = "isReturnToCapital", required = false) ActiveInactive isReturnToCapital) {
        return projectAgreementService.getByReturnStatus(projectCodes, isReturnToFlow, isReturnToCapital);
    }

    /**
     * 根据项目代码和流程贷款阶段获取项目协议列表
     *
     * @param projectCode      项目编码
     * @param flowLoanStage    资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @return 项目协议列表
     */
    @PostMapping("/queryByStage")
    @AnonymousAccess
    public List<ProjectAgreement> getByStage(
            @RequestParam("projectCode") String projectCode,
            @RequestParam(value = "flowLoanStage", required = false) String flowLoanStage,
            @RequestParam(value = "capitalLoanStage", required = false) String capitalLoanStage) {
        return projectAgreementService.getByStage(projectCode, flowLoanStage, capitalLoanStage);
    }


    @GetMapping("/page")
    @ApiOperation("查询资产列表分页")
    @PreAuthorize("@el.check('projectAgreement:page')")
    public RestResult<PageResult<ProjectAgreement>> page(ProjectAgreementQueryCriteria criteria){
        return RestResult.success(projectAgreementService.queryAllPage(criteria));
    }

    @GetMapping("/info")
    @PreAuthorize("@el.check('projectAgreement:info')")
    public RestResult<ProjectAgreement> info(@RequestParam String id){
        return RestResult.success(projectAgreementService.info(id));
    }

    @PostMapping("/create")
    @ApiOperation("新增资产控制")
    @PreAuthorize("@el.check('projectAgreement:add')")
    public RestResult<Object> create(@Validated @RequestBody ProjectAgreementDto resources){
        projectAgreementService.create(resources);
        return RestResult.success();
    }

    @PostMapping("/update")
    @ApiOperation("修改资产控制")
    @PreAuthorize("@el.check('projectAgreement:edit')")
    public RestResult<Object> update(@Validated @RequestBody ProjectAgreementDto resources){
        projectAgreementService.update(resources);
        return RestResult.success();
    }

    @PostMapping("/enable")
    @ApiOperation("启用禁用")
    @PreAuthorize("@el.check('projectAgreement:enable')")
    public RestResult<Object> enable(@Validated @RequestBody ProjectAgreementDto resources){
        projectAgreementService.enable(resources);
        return RestResult.success();
    }

    @PostMapping("/del")
    @ApiOperation("删除资产控制")
    @PreAuthorize("@el.check('projectAgreement:del')")
    public RestResult<Object> deleteFlowConfig(@ApiParam(value = "传ID数组[]") @RequestBody List<String> ids) {
        projectAgreementService.deleteAll(ids);
        return RestResult.success(HttpStatus.OK);
    }

}
