<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!-- 搜索 -->
      <el-form :model="query" ref="queryForm" :inline="true">
        <el-form-item>
          <label class="el-form-item-label">项目编码</label>
          <el-input v-model="query.projectCode" clearable placeholder="请输入项目编码" style="width: 185px;" class="filter-item" />
        </el-form-item>
        <el-form-item>
          <label class="el-form-item-label">项目名称</label>
          <el-input v-model="query.projectName" clearable placeholder="请输入项目名称" style="width: 185px;" class="filter-item" />
        </el-form-item>
        <el-form-item>
          <label class="el-form-item-label">项目类型</label>
          <el-select v-model="query.projectTypeCode" clearable size="small" placeholder="请选择项目类型" class="filter-item" style="width: 90px" >
            <el-option v-for="item in projectTypeCode" :key="item.label" :label="item.value" :value="item.label" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <label class="el-form-item-label">项目状态</label>
          <el-select v-model="query.enabled" clearable size="small" placeholder="请选择项目状态" class="filter-item" style="width: 90px" >
            <el-option v-for="item in ableStatusExt" :key="item.label" :label="item.value" :value="item.label" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <label class="el-form-item-label">资产方名称</label>
            <el-select v-model="query.flowChannel" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 150px;" >
              <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
            </el-select>
        </el-form-item>
        <el-form-item>
          <label class="el-form-item-label">资金方名称</label>
          <el-select v-model="query.capitalChannel" clearable size="small" placeholder="请选择资金方" class="filter-item" style="width: 150px;" >
            <el-option v-for="item in fundList" :key="item.bankChannel" :label="item.capitalNameShort" :value="item.bankChannel" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-permission="permission.page" round size="mini" type="primary" icon="el-icon-search" @click="search">搜索</el-button>
          <el-button v-permission="permission.page" round size="mini" icon="el-icon-refresh-left" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
      <!--列表按钮-->
      <el-button
        v-permission="permission.add"
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-plus"
        @click="openDialog"
      >
        新增
      </el-button>
      <el-button
        v-permission="permission.edit"
        class="filter-item"
        size="mini"
        type="success"
        icon="el-icon-edit"
        @click="jumpPage('edit')"
      >
        修改
      </el-button>
      <el-button
        v-permission="permission.info"
        class="filter-item"
        size="mini"
        type="info"
        icon="el-icon-document"
        @click="jumpPage('detail')"
      >
        查看
      </el-button>
      <el-button
        v-permission="permission.enable"
        class="filter-item"
        size="mini"
        type="danger"
        icon="el-icon-circle-close"
        @click="stopUse"
      >
        启用/停用
      </el-button>
      <!--表单组件-->
      <el-dialog :visible.sync="dialogVisible" :close-on-click-modal="false" title="新增项目" width="580px" :show-close="false">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
          <el-form-item label="项目编码" prop="projectCode">
            <el-input v-model="form.projectCode" clearable placeholder="请输入项目编码" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="form.projectName" style="width: 370px;" clearable placeholder="请输入项目名称" />
          </el-form-item>
          <el-form-item label="资产方" prop="flowChannel">
            <el-select v-model="form.flowChannel" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 370px;" >
              <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
            </el-select>
          </el-form-item>
          <el-form-item label="融担方" prop="guaranteeCode">
            <el-select v-model="form.guaranteeCode" clearable size="small" placeholder="请选择融担方" class="filter-item" style="width: 370px;" >
              <el-option v-for="item in guaranteeList" :key="item.guaranteeCode" :label="item.guaranteeNameShort" :value="item.guaranteeCode" />
            </el-select>
          </el-form-item>
          <el-form-item label="资金方" prop="capitalChannel">
            <el-select v-model="form.capitalChannel" clearable size="small" placeholder="请选择资金方" class="filter-item" style="width: 370px;" >
              <el-option v-for="item in fundList" :key="item.bankChannel" :label="item.capitalNameShort" :value="item.bankChannel" />
            </el-select>
          </el-form-item>
          <el-form-item label="项目类型" prop="projectTypeCode">
            <el-select v-model="form.projectTypeCode" clearable size="small" placeholder="请选择项目类型" class="filter-item" style="width: 370px;" >
              <el-option v-for="item in projectTypeCode" :key="item.label" :label="item.value" :value="item.label" />
            </el-select>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="saveProject">保 存</el-button>
        </span>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" border="border" :data="data" size="small" style="width: 100%;" @selection-change="handleSelectionChange"  v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column prop="projectCode" label="项目编码" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="enabled" label="项目状态" :formatter = "(row, column, cellValue) => {const found = ableStatusExt.find(item => item.label === cellValue); return found ? found.value : cellValue}" />
        <el-table-column prop="flowChannel" label="资产方" :formatter = "(row, column, cellValue) => {const found = assetList.find(item => item.flowChannel === cellValue); return found ? found.flowNameShort : cellValue}"/>
        <el-table-column prop="guaranteeCode" label="融担方" :formatter = "(row, column, cellValue) => {const found = guaranteeList.find(item => item.guaranteeCode === cellValue); return found ? found.guaranteeNameShort : cellValue}"/>
        <el-table-column prop="capitalChannel" label="资金方" :formatter = "(row, column, cellValue) => {const found = fundList.find(item => item.bankChannel === cellValue); return found ? found.capitalNameShort : cellValue}"/>
        <el-table-column prop="projectTypeCode" label="项目类型" :formatter = "(row, column, cellValue) => {const found = projectTypeCode.find(item => item.label === cellValue); return found ? found.value : cellValue}" />
        <el-table-column prop="dailyCreditLimit" label="日授信限额" />
        <el-table-column prop="dailyLoanLimit" label="日放款限额" />
        <el-table-column prop="userName" label="更新人" />
        <el-table-column prop="updatedTime" label="更新时间" />
      </el-table>
      <!--分页组件-->
      <Pagination :pageNum="query.pageNum" :total="query.total" :pageSize="query.pageSize"
        @sizeChange="sizeChange" @currentChange="currentChange" />
    </div>
  </div>
</template>

<script>
import { getList, addProject, updateStatus, getAssetList, getGuaranteeList, getFundList } from '@/api/projectManage/project'
import { get as getDictByName } from "@/api/system/dictDetail"

export default {
  name: 'ProjectList',
  data() {
    return {
      // 查询参数
      query: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 字典
      ableStatusExt: [],
      projectTypeCode: [],
      // 选取数据
      selectedRows: {},
      // 弹窗开关
      dialogVisible: false,
      // 资产方选择
      assetList: [],
      // 融担方选择
      guaranteeList: [],
      // 资金方选择
      fundList: [],
      // 新增表单数据
      form: {},
      // 主列表数据
      data: [],
      // 加载效果
      loading: false,
      // 按钮权限
      permission: {
        page: ['admin', 'projectInfo:page'],
        add: ['admin', 'projectInfo:add'],
        edit: ['admin', 'projectInfo:edit'],
        info: ['admin', 'projectInfo:info'],
        enable: ['admin', 'projectInfo:enable']
      },
      // 校验规则
      rules: {
        projectCode: [
          { required: true, message: '项目编码不能为空', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' }
        ],
        flowChannel: [
          { required: true, message: '资产方不能为空', trigger: 'blur' }
        ],
        guaranteeCode: [
          { required: true, message: '融担方不能为空', trigger: 'blur' }
        ],
        capitalChannel: [
          { required: true, message: '资金方不能为空', trigger: 'blur' }
        ],
        projectTypeCode: [
          { required: true, message: '项目类型不能为空', trigger: 'blur' }
        ]
      }
    }
  },

  // 钩子函数
  created() {
    // 获取项目状态字典
    getDictByName("ableStatusExt").then(res => {
      this.ableStatusExt = res.content;
    });
    // 获取项目类型字典
    getDictByName("projectTypeCode").then(res => {
      this.projectTypeCode = res.content;
    });
    this.queryList(this.query);
    this.queryAsset({pageNum: 1, pageSize: 99999,});
    this.queryGuarantee({pageNum: 1, pageSize: 99999,});
    this.queryFund({pageNum: 1, pageSize: 99999,});
  },

  methods: {

    // 搜索
    search() {
      this.queryList(this.query)
    },

    // 查询项目主列表
    queryList(params) {
      this.loading = true;
      params.projectDurationType = 'LONGTIME';
      getList(params).then((res) => {
        if(res.code === '000000') {
          this.data = res.data.content;
          this.query.total = res.data.totalElements;
        };
        this.loading = false;
      })
    },

    // 查询资产方主列表
    queryAsset(params) {
      params.enabled = 'ENABLE';
      getAssetList(params).then((res) => {
        if(res.code === '000000') {
          this.assetList = res.data.content;
        };
      })
    },

    // 查询融担方主列表
    queryGuarantee(params) {
      params.enabled = 'ENABLE';
      getGuaranteeList(params).then((res) => {
        if(res.code === '000000') {
          this.guaranteeList = res.data.content;
        };
      })
    },

    // 查询资金方主列表
    queryFund(params) {
      params.enabled = 'ENABLE';
      getFundList(params).then((res) => {
        if(res.code === '000000') {
          this.fundList = res.data.content;
        };
      })
    },

    // 新增长期项目
    saveProject() {
      this.$refs['form'].validate(valid => {
        if(valid) {
          let element = {
            projectDurationType: 'LONGTIME'
          };
          this.form.elements = element;
          addProject(this.form).then((res) => {
            if(res.code === '000000') {
              this.dialogVisible = false;
              this.$router.push({ name: 'ProjectDtl',   query: { id: res.data.id, type: 'add', projectCode: res.data.projectCode } });
              this.form = {};
              this.$message({
                message: '项目新增成功!',
                type: 'success'
              });
            } else {
              this.$message({
                message: '项目新增失败!',
                type: 'error'
              });
            }
          })
        }
      })
    },

    // 点击页码及上一页下一页按钮操作
    currentChange(val) {
      this.query.pageNum = val;
      this.queryList(this.query); //调用接口方法
    },

    //每页展示几条按钮操作
    sizeChange(val) {
      this.query.pageSize = val;
      this.queryList(this.query); //调用接口方法
    },

    // 启用/停用
    stopUse() {
      if (Object.keys(this.selectedRows).length !== 0) {
        this.$confirm("确定启用/停用当前项目？","警告",{type:"warning"}).then(()=>{
          const params = {
            id: this.selectedRows.id,
            enabled: this.selectedRows.enabled === 'INIT' ? 'ENABLE' : this.selectedRows.enabled === 'ENABLE' ? 'DISABLE' : 'ENABLE'
          }
          updateStatus(params).then((res) => {
            if(res.code === '000000') {
              this.queryList(this.query);
              this.$message({
                message: '项目启用/停用成功!',
                type: 'success'
              });
            } else {
              this.$message({
                message: '项目启用/停用失败!',
                type: 'error'
              });
            }
          })
        }).catch(()=>{

        });
      } else {
        this.$message({
          message: '请选择一个项目!',
          type: 'warning'
        });
      }
    },

    // 跳转详情页
    jumpPage(type) {
      if (Object.keys(this.selectedRows).length !== 0) {
        this.$router.push({ name: 'ProjectDtl',   query: { id: this.selectedRows.id, type: type, projectCode: this.selectedRows.projectCode } });
      } else {
        this.$message({
          message: '请选择一个项目!',
          type: 'warning'
        });
      }
    },

    // 打开新增弹窗
    openDialog() {
      this.dialogVisible = true;
    },

    // 关闭新增弹窗
    closeDialog() {
      this.dialogVisible = false;
      this.form = {};
      this.$refs['form'].clearValidate();
    },

    // 重置搜索条件
    reset() {
      this.query = {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
      this.queryList(this.query)
    },

    // 控制单选
    handleSelectionChange(val) {
      if (val.length > 1) {
        this.$refs.table.clearSelection();
        this.$refs.table.toggleRowSelection(val[val.length - 1]);
      }
      this.selectedRows = val[val.length - 1];
      if(!this.selectedRows) {
        this.selectedRows = {}
      }
    }
  }
}
</script>

<style scoped>
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}
</style>
